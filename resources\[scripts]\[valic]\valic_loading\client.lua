local isLoadingScreenActive = true
local loadingProgress = 0

-- Funkce pro aktualizaci loading progress
local function updateLoadingProgress(progress, text)
    if isLoadingScreenActive then
        SendLoadingScreenMessage(json.encode({
            eventName = 'loadProgress',
            loadFraction = progress / 100,
            loadingText = text or "Načítání..."
        }))
    end
end

-- HTTP callback handler pro loading screen
RegisterNUICallback('loadingComplete', function(data, cb)
    if isLoadingScreenActive then
        print("^2[valic_loading] Loading screen fade out completed, shutting down^7")
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
    cb('ok')
end)

-- Hlavní loading loop
Citizen.CreateThread(function()
    local startTime = GetGameTimer()
    local maxLoadTime = 45000 -- 45 sekund maximální doba načítání

    while isLoadingScreenActive do
        -- Pokus o získání loading progress z FiveM
        local gameProgress = nil

        -- Bezpečné volání GetLoadingScreenLoadFraction
        local success, result = pcall(function()
            return GetLoadingScreenLoadFraction()
        end)

        if success and result then
            gameProgress = result
        end

        local progressPercent = 0

        if gameProgress and gameProgress > 0 then
            -- Použij skutečný progress z FiveM
            progressPercent = math.floor(gameProgress * 100)
        else
            -- Fallback: simuluj progress na základě času
            local elapsedTime = GetGameTimer() - startTime
            progressPercent = math.min(math.floor((elapsedTime / maxLoadTime) * 100), 99)
        end

        -- Aktualizuj pouze pokud se progress změnil
        if progressPercent ~= loadingProgress then
            loadingProgress = progressPercent

            -- Přidej loading text na základě progress
            local loadingText = "Načítání..."
            if progressPercent >= 10 then loadingText = "Načítání světa..." end
            if progressPercent >= 25 then loadingText = "Načítání vozidel..." end
            if progressPercent >= 50 then loadingText = "Načítání objektů..." end
            if progressPercent >= 75 then loadingText = "Dokončování..." end
            if progressPercent >= 90 then loadingText = "Téměř hotovo..." end

            updateLoadingProgress(progressPercent, loadingText)
        end

        -- Pokud je loading dokončen nebo timeout
        local elapsedTime = GetGameTimer() - startTime
        if progressPercent >= 100 or elapsedTime >= maxLoadTime then
            Citizen.Wait(2000) -- Počkej 2 sekundy před zavřením
            ShutdownLoadingScreen()
            isLoadingScreenActive = false
            break
        end

        Citizen.Wait(100) -- Kontroluj každých 100ms
    end
end)

-- Event handlery pro custom loading stavy
RegisterNetEvent('valic_loading:updateProgress')
AddEventHandler('valic_loading:updateProgress', function(progress, text)
    updateLoadingProgress(progress, text)
end)

RegisterNetEvent('valic_loading:closeLoadingScreen')
AddEventHandler('valic_loading:closeLoadingScreen', function()
    if isLoadingScreenActive then
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)

-- Automatické zavření loading screenu po připojení
AddEventHandler('playerSpawned', function()
    if isLoadingScreenActive then
        Citizen.Wait(1000)
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)

-- Backup timer pro případ, že se loading screen nezavře automaticky
Citizen.CreateThread(function()
    Citizen.Wait(60000) -- 60 sekund timeout
    if isLoadingScreenActive then
        print("^3[valic_loading] Loading screen timeout - force closing^7")
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)